import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { Chapter } from '@modules/chapters/entities/chapter.entity';
import { MajorCourse } from '@modules/major-courses/entities/major-course.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('courses')
export class Course {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty()
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @ApiProperty()
  @Column({ type: 'varchar', length: 255, nullable: false, default: '' })
  description?: string;

  @ApiProperty()
  @Column({
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 1.0,
    nullable: false,
    transformer: { to: (v: number) => v, from: (v: string) => parseFloat(v) },
  })
  coefficient: number;

  @ApiProperty()
  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    default: 0.0,
    nullable: false,
    comment: 'Negative marking factor (0.00 to 1.00)',
    transformer: { to: (v: number) => v, from: (v: string) => parseFloat(v) },
  })
  negative_marking_factor: number;

  @ApiProperty()
  @OneToMany(() => Chapter, (chapter) => chapter.course)
  chapters?: Chapter[];

  @ApiProperty()
  @OneToMany(() => MajorCourse, (majorCourse) => majorCourse.course)
  majorCourses?: MajorCourse[];
  // ... timestamps
}
