import { Module } from '@nestjs/common';
import { CoursesService } from '@modules/courses/courses.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Course } from './entities/course.entity';
import { Chapter } from '@modules/chapters/entities/chapter.entity';
import { CoursesController } from './courses.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Course, Chapter])],
  providers: [CoursesService],
  controllers: [CoursesController],
})
export class CoursesModule {}
