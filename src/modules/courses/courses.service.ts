import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateCourseDto } from './dto/create-course.dto';
import { UpdateCourseDto } from './dto/update-course.dto';
import { Course } from './entities/course.entity';
import { Chapter } from '@modules/chapters/entities/chapter.entity';
import { MajorCourse } from '@modules/major-courses/entities/major-course.entity';

@Injectable()
export class CoursesService {
  constructor(
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(Chapter)
    private readonly chapterRepository: Repository<Chapter>,
    @InjectRepository(MajorCourse)
    private readonly majorCourseRepository: Repository<MajorCourse>,
  ) {}

  async create(createCourseDto: CreateCourseDto) {
    const newCourse = this.courseRepository.create(createCourseDto);
    console.log(newCourse, createCourseDto);
    return await this.courseRepository.save(newCourse);
  }

  async findAll() {
    return await this.courseRepository.find();
  }

  async findOne(id: number) {
    const course = await this.courseRepository.findOneBy({ id });
    if (!course) throw new NotFoundException('Course not found');
    return course;
  }

  async findChapters(courseId: number) {
    // First verify the course exists
    const course = await this.courseRepository.findOneBy({ id: courseId });
    if (!course) throw new NotFoundException('Course not found');

    // Find all chapters for this course
    return await this.chapterRepository.find({
      where: { course_id: courseId },
      relations: ['course'],
      order: { order_in_course: 'ASC' },
    });
  }

  async findMajors(courseId: number) {
    // First verify the course exists
    const course = await this.courseRepository.findOneBy({ id: courseId });
    if (!course) throw new NotFoundException('Course not found');

    // Find all majors for this course through the junction table
    const majorCourses = await this.majorCourseRepository.find({
      where: { courseId },
      relations: ['major'],
    });

    // Extract just the majors from the junction table results
    return majorCourses.map(mc => mc.major);
  }

  async update(id: number, updateCourseDto: UpdateCourseDto) {
    const course = await this.courseRepository.preload({
      id,
      ...updateCourseDto,
    });
    if (!course) throw new NotFoundException('Course not found');
    return await this.courseRepository.save(course);
  }

  async remove(id: number) {
    const course = await this.courseRepository.findOneBy({ id });
    if (!course) throw new NotFoundException('Course not found');
    await this.courseRepository.remove(course);
    return course;
  }
}
