import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsDateString,
  IsOptional,
  IsString,
  IsDecimal,
} from 'class-validator';

export class CreateSessionDto {
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  userId: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  examId: number;

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  start_time: Date;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  end_time?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty()
  @IsOptional()
  @IsDecimal()
  score?: number;
}
