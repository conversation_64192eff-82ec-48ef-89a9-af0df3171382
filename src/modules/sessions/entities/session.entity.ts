import { Answer as UserAnswer } from '@modules/answers/entites/answer.entity';
import { Exam } from '@modules/exams/entities/exam.entity';
import { User } from '@modules/users/entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
} from 'typeorm';

@Entity('user_exam_sessions')
export class Session {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty()
  @ManyToOne(() => User, (user) => user.sessions, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  user: User;

  @ApiProperty()
  @ManyToOne(() => Exam, (exam) => exam.sessions, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  exam: Exam;

  @ApiProperty()
  @Column({ type: 'timestamp with time zone' })
  start_time: Date;

  @ApiProperty()
  @Column({ type: 'timestamp with time zone', nullable: true })
  end_time?: Date;

  @ApiProperty()
  @Column({
    type: 'varchar',
    length: 50,
    default: 'in-progress',
    nullable: false,
  })
  status: string;

  @ApiProperty()
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  score?: number;

  @ApiProperty()
  @OneToMany(() => UserAnswer, (answer) => answer.session, { cascade: true })
  answers: UserAnswer[];
}
