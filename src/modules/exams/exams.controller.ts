import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ExamsService } from './exams.service';
import { Exam } from './entities/exam.entity';
import { CreateExamDto } from './dto/create-exam.dto';

@ApiTags('exams')
@Controller('exams')
export class ExamsController {
  constructor(private readonly examsService: ExamsService) {}

  @Post()
  @UsePipes(new ValidationPipe())
  @ApiOperation({ summary: 'Create a exam' })
  @ApiResponse({ status: 201, description: 'Exam created.' })
  create(@Body() createExamDto: CreateExamDto) {
    return this.examsService.create(createExamDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all exams' })
  @ApiResponse({ status: 200, description: 'List of exams.' })
  findAll() {
    return this.examsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a exam by id' })
  @ApiResponse({ status: 200, description: 'Exam found.' })
  findOne(@Param('id') id: string) {
    return this.examsService.findOne(+id);
  }

  @Get(':id/questions')
  @ApiOperation({ summary: 'Get all questions for an exam' })
  @ApiResponse({ status: 200, description: 'List of questions for the exam.' })
  findQuestions(@Param('id') id: string) {
    return this.examsService.findQuestions(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a exam' })
  @ApiResponse({ status: 200, description: 'Exam updated.' })
  update(@Param('id') id: string, @Body() updateExamDto: Partial<Exam>) {
    return this.examsService.update(+id, updateExamDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a exam' })
  @ApiResponse({ status: 200, description: 'Exam deleted.' })
  remove(@Param('id') id: string) {
    return this.examsService.remove(+id);
  }
}
