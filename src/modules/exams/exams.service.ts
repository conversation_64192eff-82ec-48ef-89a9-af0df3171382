import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Exam } from './entities/exam.entity';
import { CreateExamDto } from './dto/create-exam.dto';

@Injectable()
export class ExamsService {
  constructor(
    @InjectRepository(Exam)
    private readonly examRepository: Repository<Exam>,
  ) {}

  async create(createExamDto: CreateExamDto) {
    const exam = this.examRepository.create(createExamDto);
    return this.examRepository.save(exam);
  }

  async findAll() {
    return this.examRepository.find({ relations: ['options'] });
  }

  async findOne(id: number) {
    const exam = await this.examRepository.findOne({
      where: { id },
      relations: ['options'],
    });
    if (!exam) throw new NotFoundException('Exam not found');
    return exam;
  }

  async update(id: number, updateExamDto: Partial<Exam>) {
    const exam = await this.examRepository.preload({
      id,
      ...updateExamDto,
    });
    if (!exam) throw new NotFoundException('Exam not found');
    return this.examRepository.save(exam);
  }

  async remove(id: number) {
    const exam = await this.examRepository.findOne({ where: { id } });
    if (!exam) throw new NotFoundException('Exam not found');
    await this.examRepository.remove(exam);
    return exam;
  }
}
