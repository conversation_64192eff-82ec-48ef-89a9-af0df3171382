import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsString,
  IsOptional,
  IsArray,
} from 'class-validator';

export class CreateExamDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  duration_minutes: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  instructions?: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  majorId: number;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  questionIds?: number[];
}
