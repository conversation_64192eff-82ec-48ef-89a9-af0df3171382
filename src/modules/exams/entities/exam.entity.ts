import { Major } from '@modules/majors/entities/major.entity';
import { Question } from '@modules/questions/entities/question.entity';
import { Session as UserExamSession } from '@modules/sessions/entities/session.entity';
import { ApiProperty } from '@nestjs/swagger';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';

@Entity('exams')
export class Exam {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty()
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @ApiProperty()
  @Column({ type: 'integer', nullable: false })
  duration_minutes: number;

  @ApiProperty()
  @Column({ type: 'text', nullable: true })
  instructions?: string;

  @ApiProperty()
  @ManyToOne(() => Major, { nullable: false, onDelete: 'CASCADE' })
  major: Major;

  @ApiProperty()
  @ManyToMany(() => Question)
  @JoinTable({
    name: 'exam_questions',
    joinColumn: { name: 'exam_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'question_id', referencedColumnName: 'id' },
  })
  questions: Question[];

  @ApiProperty()
  @OneToMany(() => UserExamSession, (session) => session.exam)
  sessions: UserExamSession[];

  @ApiProperty()
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @ApiProperty()
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;
}
