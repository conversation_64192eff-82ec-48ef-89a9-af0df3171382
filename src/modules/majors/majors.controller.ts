import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Put,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MajorsService } from './majors.service';
import { CreateMajorDto } from './dto/create-major.dto';
import { UpdateMajorDto } from './dto/update-major.dto';

@ApiTags('majors')
@Controller('majors')
export class MajorsController {
  constructor(private readonly majorsService: MajorsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a major' })
  @ApiResponse({ status: 201, description: 'Major created.' })
  create(@Body() createMajorDto: CreateMajorDto) {
    return this.majorsService.create(createMajorDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all majors' })
  @ApiResponse({ status: 200, description: 'List of majors.' })
  findAll() {
    return this.majorsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a major by id' })
  @ApiResponse({ status: 200, description: 'Major found.' })
  findOne(@Param('id') id: string) {
    return this.majorsService.findOne(+id);
  }

  @Get(':id/courses')
  @ApiOperation({ summary: 'Get all courses for a major' })
  @ApiResponse({ status: 200, description: 'List of courses for the major.' })
  findCourses(@Param('id') id: string) {
    return this.majorsService.findCourses(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a major' })
  @ApiResponse({ status: 200, description: 'Major updated.' })
  update(@Param('id') id: string, @Body() updateMajorDto: UpdateMajorDto) {
    return this.majorsService.update(+id, updateMajorDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a major' })
  @ApiResponse({ status: 200, description: 'Major updated.' })
  updatea(@Param('id') id: string, @Body() updateMajorDto: UpdateMajorDto) {
    return this.majorsService.update(+id, updateMajorDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a major' })
  @ApiResponse({ status: 200, description: 'Major deleted.' })
  remove(@Param('id') id: string) {
    return this.majorsService.remove(+id);
  }
}
