import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { MajorCourse } from '@modules/major-courses/entities/major-course.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('majors')
export class Major {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number;
  @Column({ type: 'varchar', length: 255, unique: false, nullable: false })
  name: string;

  @ApiProperty()
  @Column({ type: 'text', nullable: false, default: '' })
  description?: string;

  @ApiProperty()
  @OneToMany(() => MajorCourse, (majorCourse) => majorCourse.major)
  majorCourses: MajorCourse[];

  @ApiProperty()
  @CreateDateColumn({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;
}
