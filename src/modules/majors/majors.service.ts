import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateMajorDto } from './dto/create-major.dto';
import { UpdateMajorDto } from './dto/update-major.dto';
import { Major } from './entities/major.entity';

@Injectable()
export class MajorsService {
  constructor(
    @InjectRepository(Major)
    private readonly majorRepository: Repository<Major>,
  ) {}

  async create(createMajorDto: CreateMajorDto) {
    const newMajor = this.majorRepository.create(createMajorDto);
    return await this.majorRepository.save(newMajor);
  }

  async findAll() {
    return await this.majorRepository.find();
  }

  async findOne(id: number) {
    const major = await this.majorRepository.findOneBy({ id });
    if (!major) throw new NotFoundException('Major not found');
    return major;
  }

  async update(id: number, updateMajorDto: UpdateMajorDto) {
    const major = await this.majorRepository.preload({
      id,
      ...updateMajorDto,
    });
    if (!major) throw new NotFoundException('Major not found');
    return await this.majorRepository.save(major);
  }

  async remove(id: number) {
    const major = await this.majorRepository.findOneBy({ id });
    if (!major) throw new NotFoundException('Major not found');
    await this.majorRepository.remove(major);
    return major;
  }
}
