import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateMajorDto } from './dto/create-major.dto';
import { UpdateMajorDto } from './dto/update-major.dto';
import { Major } from './entities/major.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { MajorCourse } from '@modules/major-courses/entities/major-course.entity';

@Injectable()
export class MajorsService {
  constructor(
    @InjectRepository(Major)
    private readonly majorRepository: Repository<Major>,
    @InjectRepository(MajorCourse)
    private readonly majorCourseRepository: Repository<MajorCourse>,
  ) {}

  async create(createMajorDto: CreateMajorDto) {
    const newMajor = this.majorRepository.create(createMajorDto);
    return await this.majorRepository.save(newMajor);
  }

  async findAll() {
    return await this.majorRepository.find();
  }

  async findOne(id: number) {
    const major = await this.majorRepository.findOneBy({ id });
    if (!major) throw new NotFoundException('Major not found');
    return major;
  }

  async findCourses(majorId: number) {
    // First verify the major exists
    const major = await this.majorRepository.findOneBy({ id: majorId });
    if (!major) throw new NotFoundException('Major not found');

    // Find all courses for this major through the junction table
    const majorCourses = await this.majorCourseRepository.find({
      where: { majorId },
      relations: ['course'],
    });

    // Extract just the courses from the junction table results
    return majorCourses.map(mc => mc.course);
  }

  async update(id: number, updateMajorDto: UpdateMajorDto) {
    const major = await this.majorRepository.preload({
      id,
      ...updateMajorDto,
    });
    if (!major) throw new NotFoundException('Major not found');
    return await this.majorRepository.save(major);
  }

  async remove(id: number) {
    const major = await this.majorRepository.findOneBy({ id });
    if (!major) throw new NotFoundException('Major not found');
    await this.majorRepository.remove(major);
    return major;
  }
}
