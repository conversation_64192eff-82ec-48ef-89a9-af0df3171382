import { Module } from '@nestjs/common';
import { MajorsService } from './majors.service';
import { Major } from './entities/major.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MajorCourse } from '@modules/major-courses/entities/major-course.entity';
import { MajorsController } from './majors.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Major, MajorCourse])],
  providers: [MajorsService],
  exports: [MajorsService],
  controllers: [MajorsController],
})
export class MajorsModule {}
