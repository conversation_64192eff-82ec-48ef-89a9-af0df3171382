import { Module } from '@nestjs/common';
import { MajorCoursesService } from './major-courses.service';
import { MajorCourse } from './entities/major-course.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MajorCoursesController } from './major-courses.controller';

@Module({
  imports: [TypeOrmModule.forFeature([MajorCourse])],
  providers: [MajorCoursesService],
  controllers: [MajorCoursesController],
  exports: [MajorCoursesService],
})
export class MajorCoursesModule {}
