import { Entity, PrimaryColumn, ManyToOne, CreateDateColumn } from 'typeorm';
import { Major } from '@modules/majors/entities/major.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('major_courses')
export class MajorCourse {
  @ApiProperty()
  @PrimaryColumn()
  majorId: number;

  @ApiProperty()
  @PrimaryColumn()
  courseId: number;

  @ApiProperty()
  @ManyToOne(() => Major, (major) => major.majorCourses, {
    onDelete: 'CASCADE',
  })
  major: Major;

  @ApiProperty()
  @ManyToOne(() => Course, (course) => course.majorCourses, {
    onDelete: 'CASCADE',
  })
  course: Course;

  @ApiProperty()
  @CreateDateColumn({ type: 'timestamp with time zone' })
  createdAt: Date;
}
