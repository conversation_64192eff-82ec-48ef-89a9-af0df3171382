import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MajorCoursesService } from './major-courses.service';
import { MajorCourse } from './entities/major-course.entity';

@ApiTags('major-courses')
@Controller('major-courses')
export class MajorCoursesController {
  constructor(private readonly majorCoursesService: MajorCoursesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a major-course relation' })
  @ApiResponse({ status: 201, description: 'MajorCourse created.' })
  create(@Body() dto: Partial<MajorCourse>) {
    return this.majorCoursesService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all major-course relations' })
  @ApiResponse({ status: 200, description: 'List of major-courses.' })
  findAll() {
    return this.majorCoursesService.findAll();
  }

  @Get(':majorId/:courseId')
  @ApiOperation({ summary: 'Get a major-course by ids' })
  @ApiResponse({ status: 200, description: 'MajorCourse found.' })
  findOne(
    @Param('majorId') majorId: string,
    @Param('courseId') courseId: string,
  ) {
    return this.majorCoursesService.findOne(+majorId, +courseId);
  }

  @Patch(':majorId/:courseId')
  @ApiOperation({ summary: 'Update a major-course relation' })
  @ApiResponse({ status: 200, description: 'MajorCourse updated.' })
  update(
    @Param('majorId') majorId: string,
    @Param('courseId') courseId: string,
    @Body() dto: Partial<MajorCourse>,
  ) {
    return this.majorCoursesService.update(+majorId, +courseId, dto);
  }

  @Delete(':majorId/:courseId')
  @ApiOperation({ summary: 'Delete a major-course relation' })
  @ApiResponse({ status: 200, description: 'MajorCourse deleted.' })
  remove(
    @Param('majorId') majorId: string,
    @Param('courseId') courseId: string,
  ) {
    return this.majorCoursesService.remove(+majorId, +courseId);
  }
}
