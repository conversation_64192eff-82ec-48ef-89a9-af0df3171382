import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MajorCourse } from './entities/major-course.entity';

@Injectable()
export class MajorCoursesService {
  constructor(
    @InjectRepository(MajorCourse)
    private readonly majorCourseRepository: Repository<MajorCourse>,
  ) {}

  async create(createMajorCourseDto: Partial<MajorCourse>) {
    const entity = this.majorCourseRepository.create(createMajorCourseDto);
    return this.majorCourseRepository.save(entity);
  }

  async findAll() {
    return this.majorCourseRepository.find();
  }

  async findOne(majorId: number, courseId: number) {
    const entity = await this.majorCourseRepository.findOne({
      where: { majorId, courseId },
    });
    if (!entity) throw new NotFoundException('MajorCourse not found');
    return entity;
  }

  async update(
    majorId: number,
    courseId: number,
    updateDto: Partial<MajorCourse>,
  ) {
    const entity = await this.majorCourseRepository.preload({
      majorId,
      courseId,
      ...updateDto,
    });
    if (!entity) throw new NotFoundException('MajorCourse not found');
    return this.majorCourseRepository.save(entity);
  }

  async remove(majorId: number, courseId: number) {
    const entity = await this.majorCourseRepository.findOne({
      where: { majorId, courseId },
    });
    if (!entity) throw new NotFoundException('MajorCourse not found');
    await this.majorCourseRepository.remove(entity);
    return entity;
  }
}
