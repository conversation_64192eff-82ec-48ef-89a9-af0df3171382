import {
  IsString,
  IsNotEmpty,
  IsArray,
  Validate<PERSON>ested,
  <PERSON><PERSON>umber,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateQuestionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  question_text: string;

  @ApiProperty()
  @IsNumber()
  chapter_id: number;

  // @ApiProperty()
  // @IsArray()
  // @ValidateNested({ each: true })
  // @Type(() => QuestionOptionDto)
  // options: QuestionOptionDto[];
}

// export class QuestionOptionDto {
//   @IsString()
//   @IsNotEmpty()
//   option_ttext: string;
// }
