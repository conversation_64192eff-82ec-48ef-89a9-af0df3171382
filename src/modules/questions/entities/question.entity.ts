import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Chapter } from '@modules/chapters/entities/chapter.entity';
import { Option } from '@modules/options/entities/option.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

@Entity()
export class Question {
  @ApiProperty({ example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ type: () => Chapter })
  @ManyToOne(() => Chapter, (chapter) => chapter.questions, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  @JoinColumn()
  chapter: Chapter;

  @ApiProperty({ example: 1 })
  @Column({ type: 'integer', nullable: false })
  chapter_id: number;

  @ApiProperty({ example: 'What is the capital of France?' })
  @Column({ type: 'text' })
  question_text: string;

  @ApiPropertyOptional({ example: 'easy' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  difficulty_level?: string;

  @ApiPropertyOptional({ example: 'Paris is the capital of France.' })
  @Column({ type: 'text', nullable: true })
  explanation?: string;

  @ApiProperty({ type: () => [Option] })
  @OneToMany(() => Option, (option) => option.question, { cascade: true }) // cascade for easy creation of options with questions
  options: Option[];

  @ApiProperty({ type: String, example: '2024-06-01T12:00:00Z' })
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @ApiProperty({ type: String, example: '2024-06-01T12:00:00Z' })
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;
}
