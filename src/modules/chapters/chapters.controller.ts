import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ChaptersService } from './chapters.service';
import { CreateChapterDto } from './dto/create-chapter.dto';

@ApiTags('chapters')
@Controller('chapters')
export class ChaptersController {
  constructor(private readonly chaptersService: ChaptersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a chapter' })
  @ApiResponse({ status: 201, description: 'Chapter created.' })
  create(@Body() createChapterDto: CreateChapterDto) {
    return this.chaptersService.create(createChapterDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all chapters' })
  @ApiResponse({ status: 200, description: 'List of chapters.' })
  findAll() {
    return this.chaptersService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a chapter by id' })
  @ApiResponse({ status: 200, description: 'Chapter found.' })
  findOne(@Param('id') id: string) {
    return this.chaptersService.findOne(+id);
  }

  @Get(':id/questions')
  @ApiOperation({ summary: 'Get all questions for a chapter' })
  @ApiResponse({ status: 200, description: 'List of questions for the chapter.' })
  findQuestions(@Param('id') id: string) {
    return this.chaptersService.findQuestions(+id);
  }

  @Get(':id/course')
  @ApiOperation({ summary: 'Get the course for a chapter' })
  @ApiResponse({ status: 200, description: 'Course for the chapter.' })
  findCourse(@Param('id') id: string) {
    return this.chaptersService.findCourse(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a chapter' })
  @ApiResponse({ status: 200, description: 'Chapter updated.' })
  update(
    @Param('id') id: string,
    @Body() updateChapterDto: Partial<CreateChapterDto>,
  ) {
    return this.chaptersService.update(+id, updateChapterDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a chapter' })
  @ApiResponse({ status: 200, description: 'Chapter deleted.' })
  remove(@Param('id') id: string) {
    return this.chaptersService.remove(+id);
  }
}
