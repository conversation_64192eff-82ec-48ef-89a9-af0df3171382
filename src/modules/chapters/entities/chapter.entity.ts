import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Unique,
} from 'typeorm';
import { Course } from '@modules/courses/entities/course.entity';
import { Question } from '@modules/questions/entities/question.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('chapters')
@Unique(['course', 'name'])
export class Chapter {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty()
  @ManyToOne(() => Course, (course) => course.chapters, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  course: Course;

  @ApiProperty()
  @Column({ type: 'integer', nullable: false })
  course_id: number;

  @ApiProperty()
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @ApiProperty()
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty()
  @Column({ type: 'integer', nullable: true })
  order_in_course?: number;

  @ApiProperty()
  @OneToMany(() => Question, (question) => question.chapter)
  questions: Question[];

  @ApiProperty()
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @ApiProperty()
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;
}
