import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateChapterDto } from './dto/create-chapter.dto';
import { Chapter } from './entities/chapter.entity';

@Injectable()
export class ChaptersService {
  constructor(
    @InjectRepository(Chapter)
    private readonly chapterRepository: Repository<Chapter>,
  ) {}

  async create(createChapterDto: Partial<CreateChapterDto>) {
    const chapter = this.chapterRepository.create(createChapterDto);
    return this.chapterRepository.save(chapter);
  }

  async findAll() {
    return this.chapterRepository.find();
  }

  async findOne(id: number) {
    const chapter = await this.chapterRepository.findOne({ where: { id } });
    if (!chapter) throw new NotFoundException('Chapter not found');
    return chapter;
  }

  async update(id: number, updateChapterDto: Partial<CreateChapterDto>) {
    const chapter = await this.chapterRepository.preload({
      id,
      ...updateChapterDto,
    });
    if (!chapter) throw new NotFoundException('Chapter not found');
    return this.chapterRepository.save(chapter);
  }

  async remove(id: number) {
    const chapter = await this.chapterRepository.findOne({ where: { id } });
    if (!chapter) throw new NotFoundException('Chapter not found');
    await this.chapterRepository.remove(chapter);
    return chapter;
  }
}
