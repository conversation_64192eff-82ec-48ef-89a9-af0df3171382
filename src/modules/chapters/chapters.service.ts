import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateChapterDto } from './dto/create-chapter.dto';
import { Chapter } from './entities/chapter.entity';
import { Question } from '@modules/questions/entities/question.entity';
import { Course } from '@modules/courses/entities/course.entity';

@Injectable()
export class ChaptersService {
  constructor(
    @InjectRepository(Chapter)
    private readonly chapterRepository: Repository<Chapter>,
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
  ) {}

  async create(createChapterDto: Partial<CreateChapterDto>) {
    const chapter = this.chapterRepository.create(createChapterDto);
    return this.chapterRepository.save(chapter);
  }

  async findAll() {
    return this.chapterRepository.find();
  }

  async findOne(id: number) {
    const chapter = await this.chapterRepository.findOne({ where: { id } });
    if (!chapter) throw new NotFoundException('Chapter not found');
    return chapter;
  }

  async findQuestions(chapterId: number) {
    // First verify the chapter exists
    const chapter = await this.chapterRepository.findOne({
      where: { id: chapterId },
    });
    if (!chapter) throw new NotFoundException('Chapter not found');

    // Find all questions for this chapter
    return await this.questionRepository.find({
      where: { chapter_id: chapterId },
      relations: ['chapter', 'options'],
    });
  }

  async findCourse(chapterId: number) {
    // Find the chapter with its course relation
    const chapter = await this.chapterRepository.findOne({
      where: { id: chapterId },
      relations: ['course'],
    });
    if (!chapter) throw new NotFoundException('Chapter not found');

    return chapter.course;
  }

  async update(id: number, updateChapterDto: Partial<CreateChapterDto>) {
    const chapter = await this.chapterRepository.preload({
      id,
      ...updateChapterDto,
    });
    if (!chapter) throw new NotFoundException('Chapter not found');
    return this.chapterRepository.save(chapter);
  }

  async remove(id: number) {
    const chapter = await this.chapterRepository.findOne({ where: { id } });
    if (!chapter) throw new NotFoundException('Chapter not found');
    await this.chapterRepository.remove(chapter);
    return chapter;
  }
}
