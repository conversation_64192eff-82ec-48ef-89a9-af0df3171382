import { Session as UserExamSession } from '@modules/sessions/entities/session.entity';
import { ApiProperty } from '@nestjs/swagger';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';

@Entity('users')
export class User {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty()
  @Column({ type: 'varchar', length: 255, unique: true, nullable: true })
  email?: string;

  @ApiProperty()
  @Column({ type: 'varchar', length: 11, unique: true, nullable: false })
  mobile: string;

  @ApiProperty()
  @Column({ type: 'varchar', length: 255, nullable: false, select: false })
  password_hash: string;

  @ApiProperty()
  @Column({ type: 'varchar', length: 200, nullable: false })
  first_name: string;

  @ApiProperty()
  @Column({ type: 'varchar', length: 200, nullable: false })
  last_name: string;

  @ApiProperty()
  @Column({ type: 'varchar', length: 50, default: 'student', nullable: false })
  role: string;

  @ApiProperty()
  @OneToMany(() => UserExamSession, (session) => session.user)
  sessions: UserExamSession[];

  @ApiProperty()
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @ApiProperty()
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;
}
