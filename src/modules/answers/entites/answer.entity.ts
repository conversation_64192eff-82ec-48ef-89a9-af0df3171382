import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Session as UserExamSession } from '@modules/sessions/entities/session.entity';
import { Question } from '@modules/questions/entities/question.entity';
import { Option } from '@modules/options/entities/option.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('user_answers')
export class Answer {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty()
  @ManyToOne(() => UserExamSession, (session) => session.answers, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  session: UserExamSession;

  @ApiProperty()
  @ManyToOne(() => Question, { nullable: false, onDelete: 'CASCADE' })
  question: Question;

  @ApiProperty()
  @ManyToOne(() => Option, { nullable: false, onDelete: 'CASCADE' })
  selected_option?: Option;

  @ApiProperty()
  @Column({ type: 'boolean', nullable: true })
  is_correct?: boolean;
}
