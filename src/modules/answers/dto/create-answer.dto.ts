import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsBoolean } from 'class-validator';

export class CreateAnswerDto {
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  sessionId: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  questionId: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  selectedOptionId?: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  is_correct?: boolean;
}
