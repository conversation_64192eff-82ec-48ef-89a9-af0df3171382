import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Option } from './entities/option.entity';
import { CreateOptionDto } from './dto/create-option.dto';

@Injectable()
export class OptionsService {
  constructor(
    @InjectRepository(Option)
    private readonly optionRepository: Repository<Option>,
  ) {}

  async create(createOptionDto: CreateOptionDto) {
    const option = this.optionRepository.create(createOptionDto);
    return this.optionRepository.save(option);
  }

  async findAll() {
    return this.optionRepository.find({ relations: ['question'] });
  }

  async findOne(id: number) {
    const option = await this.optionRepository.findOne({
      where: { id },
      relations: ['question'],
    });
    if (!option) throw new NotFoundException('Option not found');
    return option;
  }

  async update(id: number, updateOptionDto: Partial<Option>) {
    const option = await this.optionRepository.preload({
      id,
      ...updateOptionDto,
    });
    if (!option) throw new NotFoundException('Option not found');
    return this.optionRepository.save(option);
  }

  async remove(id: number) {
    const option = await this.optionRepository.findOne({ where: { id } });
    if (!option) throw new NotFoundException('Option not found');
    await this.optionRepository.remove(option);
    return option;
  }
}
