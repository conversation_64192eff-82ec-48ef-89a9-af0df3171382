import {
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsNumber,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOptionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  option_text: string;

  @ApiProperty()
  @IsNumber()
  question_id: number;

  @ApiProperty()
  @IsBoolean()
  is_correct: boolean;

  // @IsArray()
  // @ValidateNested({ each: true })
  // @Type(() => QuestionOptionDto)
  // options: QuestionOptionDto[];
}

// export class QuestionOptionDto {
//   @IsString()
//   @IsNotEmpty()
//   option_ttext: string;
// }
