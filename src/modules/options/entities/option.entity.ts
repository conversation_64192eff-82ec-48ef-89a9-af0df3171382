import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { Question } from '@modules/questions/entities/question.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('options')
export class Option {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty()
  @ManyToOne(() => Question, (question) => question.options, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  question: Question;

  @ApiProperty({ example: 1 })
  @Column({ type: 'integer', nullable: false })
  question_id: number;

  @ApiProperty()
  @Column({ type: 'text' })
  option_text: string;

  @ApiProperty()
  @Column({ type: 'boolean', default: false })
  is_correct: boolean;

  @ApiProperty()
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @ApiProperty()
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;
}
