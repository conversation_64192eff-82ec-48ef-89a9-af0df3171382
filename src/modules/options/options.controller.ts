import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { OptionsService } from './options.service';
import { Option } from './entities/option.entity';
import { CreateOptionDto } from './dto/create-option.dto';

@ApiTags('options')
@Controller('options')
export class OptionsController {
  constructor(private readonly optionsService: OptionsService) {}

  @Post()
  @ApiOperation({ summary: 'Create an option' })
  @ApiResponse({ status: 201, description: 'Option created.' })
  create(@Body() createOptionDto: CreateOptionDto) {
    return this.optionsService.create(createOptionDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all options' })
  @ApiResponse({ status: 200, description: 'List of options.' })
  findAll() {
    return this.optionsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an option by id' })
  @ApiResponse({ status: 200, description: 'Option found.' })
  findOne(@Param('id') id: string) {
    return this.optionsService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an option' })
  @ApiResponse({ status: 200, description: 'Option updated.' })
  update(@Param('id') id: string, @Body() updateOptionDto: Partial<Option>) {
    return this.optionsService.update(+id, updateOptionDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an option' })
  @ApiResponse({ status: 200, description: 'Option deleted.' })
  remove(@Param('id') id: string) {
    return this.optionsService.remove(+id);
  }
}
