import { Module } from '@nestjs/common';
import { OptionsService } from './options.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Option } from './entities/option.entity';
import { Question } from '@modules/questions/entities/question.entity';
import { OptionsController } from './options.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Option, Question])],
  providers: [OptionsService],
  controllers: [OptionsController],
  exports: [OptionsService],
})
export class OptionsModule {}
