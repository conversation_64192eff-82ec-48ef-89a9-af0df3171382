import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { QuestionsModule } from '@modules/questions/questions.module';
import { ValidationPipe } from '@nestjs/common';
import { MajorsModule } from '@modules/majors/majors.module';
import { CoursesModule } from '@modules/courses/courses.module';
import { MajorCoursesModule } from '@modules/major-courses/major-courses.module';
import { AnswersModule } from '@modules/answers/answers.module';
import { UsersModule } from '@modules/users/users.module';
import { SessionsModule } from '@modules/sessions/sessions.module';
import { ExamsModule } from '@modules/exams/exams.module';
import { ChaptersModule } from '@modules/chapters/chapters.module';
import { OptionsModule } from '@modules/options/options.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix('/v1/api');
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );

  // Swagger setup for only QuestionsModule
  const config = new DocumentBuilder()
    .setTitle('Azmoon API')
    .setDescription('API documentation for Azmoon project')
    .setVersion('1.0')
    // .addTag('cats')
    .build();
  const documentFactory = SwaggerModule.createDocument(app, config, {
    include: [
      MajorsModule,
      CoursesModule,
      MajorCoursesModule,
      ChaptersModule,
      QuestionsModule,
      OptionsModule,
      AnswersModule,
      SessionsModule,
      ExamsModule,
      UsersModule,
    ],
  });
  SwaggerModule.setup('api/docs', app, documentFactory);
  console.log('APP PORT: ', process.env.APP_PORT ?? 4000);
  await app.listen(process.env.APP_PORT ?? 4000);
}
bootstrap();
