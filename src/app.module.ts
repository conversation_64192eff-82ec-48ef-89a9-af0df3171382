import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MajorsModule } from '@modules/majors/majors.module';
import { OptionsModule } from '@modules/options/options.module';
import { QuestionsModule } from '@modules/questions/questions.module';
import { MajorCoursesModule } from '@modules/major-courses/major-courses.module';
// import { CourseChaptersModule } from '@modules/__DEL__course-chapters/course-chapters.module';
import { ChaptersModule } from '@modules/chapters/chapters.module';
import { CoursesModule } from '@modules/courses/courses.module';
import { MajorsController } from '@modules/majors/majors.controller';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { UsersModule } from './modules/users/users.module';
import { ExamsModule } from './modules/exams/exams.module';
import { SessionsModule } from './modules/sessions/sessions.module';
import { AnswersModule } from './modules/answers/answers.module';
import { UsersController } from './modules/users/users.controller';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: 'postgres',
      password: 'your_secure_password',
      database: 'nestdb',
      autoLoadEntities: true,
      synchronize: false,
      namingStrategy: new SnakeNamingStrategy(),
    }),
    MajorsModule,
    CoursesModule,
    ChaptersModule,
    // CourseChaptersModule,
    MajorCoursesModule,
    QuestionsModule,
    OptionsModule,
    UsersModule,
    ExamsModule,
    SessionsModule,
    AnswersModule,
  ],
  controllers: [AppController, MajorsController, UsersController],
  providers: [AppService],
})
export class AppModule {}
