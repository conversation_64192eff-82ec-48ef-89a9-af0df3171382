{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2023",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": false,
    "strictBindCallApply": true,
    "noFallthroughCasesInSwitch": true,
    "paths": {
      "@app/*": ["src/app/*"],
      "@config/*": ["src/config/*"],
      "@core/*": ["src/core/*"],
      "@decorators/*": ["src/decorators/*"],
      "@enums/*": ["src/enums/*"],
      "@exceptions/*": ["src/exceptions/*"],
      "@filters/*": ["src/filters/*"],
      "@guards/*": ["src/guards/*"],
      "@interceptors/*": ["src/interceptors/*"],
      "@interfaces/*": ["src/interfaces/*"],
      "@middlewares/*": ["src/middlewares/*"],
      "@modules/*": ["src/modules/*"],
      "@pipes/*": ["src/pipes/*"],
      "@services/*": ["src/services/*"],
    }
  }
}
