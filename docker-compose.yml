version: '3.8'

services:
  # app:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: nestjs_app
  #   restart: unless-stopped
  #   env_file:
  #     - .env
  #   environment:
  #     - NODE_ENV=development
  #   ports:
  #     - "3000:3000"
  #   volumes:
  #     - .:/usr/src/app
  #     - /usr/src/app/node_modules
  #   depends_on:
  #     - postgres
  #     - redis
  #   networks:
  #     - app-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: postgres_db
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - app-network

  # pgAdmin
  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD}
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - app-network

  # Redis
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - app-network

volumes:
  postgres-data:
  redis-data:

networks:
  app-network:
    driver: bridge